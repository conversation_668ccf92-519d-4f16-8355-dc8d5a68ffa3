import os
import yt_dlp
import sys

def download_playlist(url, output_folder="downloads"):
    """
    Download YouTube playlist to specified folder

    Args:
        url (str): YouTube playlist URL
        output_folder (str): Output directory for downloaded videos

    Returns:
        bool: True if successful, False otherwise
    """
    # Validate URL
    if not url or not url.strip():
        print("❌ Lỗi: URL không được để trống!")
        return False

    if "youtube.com" not in url and "youtu.be" not in url:
        print("❌ Lỗi: URL không phải là link YouTube hợp lệ!")
        return False

    # Create output folder
    try:
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            print(f"📁 Đã tạo thư mục: {output_folder}")
    except Exception as e:
        print(f"❌ Lỗi tạo thư mục: {e}")
        return False

    ydl_opts = {
        'outtmpl': os.path.join(output_folder, '%(playlist_index)s - %(title)s.%(ext)s'),
        'format': 'bestvideo+bestaudio/best',
        'merge_output_format': 'mp4',
        'noplaylist': False,
        'ignoreerrors': True,
        'extract_flat': False,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            print("🔍 Đang phân tích playlist...")
            ydl.download([url])
            print("✅ Tải xuống hoàn tất!")
            return True
    except yt_dlp.DownloadError as e:
        print(f"❌ Lỗi tải xuống: {e}")
        return False
    except Exception as e:
        print(f"❌ Lỗi không xác định: {e}")
        return False

if __name__ == "__main__":
    print("=== YouTube Playlist Downloader ===")

    try:
        playlist_url = input("Nhập link playlist YouTube: ").strip()

        if not playlist_url:
            print("❌ Bạn chưa nhập URL!")
            sys.exit(1)

        print("🚀 Đang bắt đầu tải về...")
        success = download_playlist(playlist_url)

        if success:
            print("🎉 Hoàn tất! Video đã lưu trong thư mục 'downloads'.")
        else:
            print("💥 Có lỗi xảy ra trong quá trình tải xuống!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⏹️ Đã dừng tải xuống!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        sys.exit(1)
