import os
import yt_dlp

def download_playlist(url, output_folder="downloads"):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    ydl_opts = {
        'outtmpl': os.path.join(output_folder, '%(playlist_index)s - %(title)s.%(ext)s'),
        'format': 'bestvideo+bestaudio/best',
        'merge_output_format': 'mp4',
        'noplaylist': False,
        'ignoreerrors': True,
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([url])

if __name__ == "__main__":
    print("=== YouTube Playlist Downloader ===")
    playlist_url = input("Nhập link playlist YouTube: ")
    print("Đang tải về... Vui lòng chờ.")
    download_playlist(playlist_url)
    print("Hoàn tất! Video đã lưu trong thư mục 'downloads'.")
