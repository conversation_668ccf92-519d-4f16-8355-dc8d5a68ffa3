import os
import yt_dlp
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

class YouTubePlaylistDownloader:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Playlist Downloader")
        self.root.geometry("600x400")
        
        # Variables
        self.output_folder = tk.StringVar(value="downloads")
        self.url_var = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # URL input
        ttk.Label(main_frame, text="YouTube Playlist URL:").grid(row=0, column=0, sticky=tk.W, pady=5)
        url_entry = ttk.Entry(main_frame, textvariable=self.url_var, width=70)
        url_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Output folder
        ttk.Label(main_frame, text="Th<PERSON> mục lưu:").grid(row=2, column=0, sticky=tk.W, pady=5)
        folder_frame = ttk.Frame(main_frame)
        folder_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Entry(folder_frame, textvariable=self.output_folder, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(folder_frame, text="Chọn thư mục", command=self.select_folder).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Download button
        self.download_btn = ttk.Button(main_frame, text="Tải xuống", command=self.start_download)
        self.download_btn.grid(row=4, column=0, pady=20)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Status text
        self.status_text = tk.Text(main_frame, height=15, width=70)
        self.status_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Scrollbar for text
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.status_text.yview)
        scrollbar.grid(row=6, column=2, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
    def select_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_folder.set(folder)
    
    def log_message(self, message):
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_download(self):
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Lỗi", "Vui lòng nhập URL playlist!")
            return
        
        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("Lỗi", "URL không phải là link YouTube hợp lệ!")
            return
        
        # Disable button and start progress
        self.download_btn.config(state='disabled')
        self.progress.start()
        self.status_text.delete(1.0, tk.END)
        
        # Start download in separate thread
        thread = threading.Thread(target=self.download_playlist, args=(url,))
        thread.daemon = True
        thread.start()
    
    def download_playlist(self, url):
        try:
            output_folder = self.output_folder.get()
            
            # Create output folder
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
                self.log_message(f"📁 Đã tạo thư mục: {output_folder}")
            
            # Custom hook for progress
            def progress_hook(d):
                if d['status'] == 'downloading':
                    filename = d.get('filename', 'Unknown')
                    self.log_message(f"⬇️ Đang tải: {os.path.basename(filename)}")
                elif d['status'] == 'finished':
                    filename = d.get('filename', 'Unknown')
                    self.log_message(f"✅ Hoàn thành: {os.path.basename(filename)}")
            
            ydl_opts = {
                'outtmpl': os.path.join(output_folder, '%(playlist_index)s - %(title)s.%(ext)s'),
                'format': 'bestvideo+bestaudio/best',
                'merge_output_format': 'mp4',
                'noplaylist': False,
                'ignoreerrors': True,
                'progress_hooks': [progress_hook],
            }
            
            self.log_message("🔍 Đang phân tích playlist...")
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            self.log_message("🎉 Tải xuống hoàn tất!")
            messagebox.showinfo("Thành công", "Tải xuống hoàn tất!")
            
        except Exception as e:
            self.log_message(f"❌ Lỗi: {str(e)}")
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
        
        finally:
            # Re-enable button and stop progress
            self.download_btn.config(state='normal')
            self.progress.stop()

if __name__ == "__main__":
    root = tk.Tk()
    app = YouTubePlaylistDownloader(root)
    root.mainloop()
